import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';
import { LocationSettings } from '@/components/LocationSettingsModal';

const LOCATION_SETTINGS_KEY = 'locationSettings';
const LAST_LOCATION_UPDATE_KEY = 'lastLocationUpdate';

// Default location settings
export const defaultLocationSettings: LocationSettings = {
  userLocation: '',
  searchRadius: 25,
  includeRemote: true,
  autoUpdateLocation: false
};

/**
 * Get stored location settings
 */
export const getLocationSettings = async (): Promise<LocationSettings> => {
  try {
    const stored = await AsyncStorage.getItem(LOCATION_SETTINGS_KEY);
    if (stored) {
      return { ...defaultLocationSettings, ...JSON.parse(stored) };
    }
  } catch (error) {
    console.error('Error getting location settings:', error);
  }
  return defaultLocationSettings;
};

/**
 * Save location settings
 */
export const saveLocationSettings = async (settings: LocationSettings): Promise<void> => {
  try {
    await AsyncStorage.setItem(LOCATION_SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving location settings:', error);
    throw error;
  }
};

/**
 * Get current device location
 */
export const getCurrentLocation = async (): Promise<{ latitude: number; longitude: number } | null> => {
  try {
    // Request permission
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      console.log('Location permission denied');
      return null;
    }

    // Get current position
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced,
      timeInterval: 10000,
      distanceInterval: 100
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude
    };
  } catch (error) {
    console.error('Error getting current location:', error);
    return null;
  }
};

/**
 * Check if location should be auto-updated
 */
export const shouldUpdateLocation = async (): Promise<boolean> => {
  try {
    const settings = await getLocationSettings();
    if (!settings.autoUpdateLocation) {
      return false;
    }

    const lastUpdate = await AsyncStorage.getItem(LAST_LOCATION_UPDATE_KEY);
    if (!lastUpdate) {
      return true;
    }

    const lastUpdateTime = parseInt(lastUpdate, 10);
    const now = Date.now();
    const hoursSinceUpdate = (now - lastUpdateTime) / (1000 * 60 * 60);

    // Update if it's been more than 6 hours
    return hoursSinceUpdate >= 6;
  } catch (error) {
    console.error('Error checking if location should update:', error);
    return false;
  }
};

/**
 * Auto-update user location if enabled and needed
 */
export const autoUpdateLocationIfNeeded = async (): Promise<LocationSettings | null> => {
  try {
    const shouldUpdate = await shouldUpdateLocation();
    if (!shouldUpdate) {
      return null;
    }

    const currentLocation = await getCurrentLocation();
    if (!currentLocation) {
      return null;
    }

    const settings = await getLocationSettings();
    const locationString = `Current Location (${currentLocation.latitude.toFixed(2)}, ${currentLocation.longitude.toFixed(2)})`;
    
    const updatedSettings: LocationSettings = {
      ...settings,
      userLocation: locationString,
      coordinates: currentLocation
    };

    await saveLocationSettings(updatedSettings);
    await AsyncStorage.setItem(LAST_LOCATION_UPDATE_KEY, Date.now().toString());

    return updatedSettings;
  } catch (error) {
    console.error('Error auto-updating location:', error);
    return null;
  }
};

/**
 * Reverse geocode coordinates to get address
 */
export const reverseGeocode = async (
  latitude: number,
  longitude: number
): Promise<string | null> => {
  try {
    const result = await Location.reverseGeocodeAsync({ latitude, longitude });
    if (result && result.length > 0) {
      const address = result[0];
      const parts = [];

      if (address.city) parts.push(address.city);
      if (address.region) parts.push(address.region);

      return parts.join(', ') || `${latitude.toFixed(2)}, ${longitude.toFixed(2)}`;
    }
  } catch (error) {
    console.error('Error reverse geocoding:', error);
  }

  return `${latitude.toFixed(2)}, ${longitude.toFixed(2)}`;
};

/**
 * Geocode a location string to coordinates using multiple services
 */
export const geocodeLocation = async (
  locationString: string
): Promise<{ latitude: number; longitude: number; formattedAddress: string } | null> => {
  if (!locationString || locationString.trim() === '') {
    return null;
  }

  try {
    // First try Expo's geocoding
    const result = await Location.geocodeAsync(locationString);
    if (result && result.length > 0) {
      const coords = result[0];

      // Get formatted address via reverse geocoding
      const formattedAddress = await reverseGeocode(coords.latitude, coords.longitude);

      return {
        latitude: coords.latitude,
        longitude: coords.longitude,
        formattedAddress: formattedAddress || locationString
      };
    }
  } catch (error) {
    console.error('Error geocoding with Expo:', error);
  }

  // Fallback to OpenStreetMap Nominatim API (free)
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(locationString)}&limit=1&addressdetails=1`
    );

    if (response.ok) {
      const data = await response.json();
      if (data && data.length > 0) {
        const result = data[0];
        return {
          latitude: parseFloat(result.lat),
          longitude: parseFloat(result.lon),
          formattedAddress: result.display_name || locationString
        };
      }
    }
  } catch (error) {
    console.error('Error geocoding with Nominatim:', error);
  }

  return null;
};

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 * Get formatted location string for display
 */
export const getFormattedLocation = (settings: LocationSettings): string => {
  if (!settings.userLocation) {
    return 'No location set';
  }
  
  if (settings.userLocation.includes('Current Location')) {
    return settings.userLocation;
  }
  
  return settings.userLocation;
};

/**
 * Get location summary for display
 */
export const getLocationSummary = (settings: LocationSettings): string => {
  const location = getFormattedLocation(settings);
  const radius = settings.searchRadius;
  const remote = settings.includeRemote ? ' + Remote' : '';
  
  if (location === 'No location set') {
    return settings.includeRemote ? 'Remote jobs only' : 'No location filter';
  }
  
  return `${location} (${radius} mi)${remote}`;
};

/**
 * Check if location permissions are granted
 */
export const hasLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.getForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

/**
 * Request location permissions
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

/**
 * Clear all location data
 */
export const clearLocationData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([LOCATION_SETTINGS_KEY, LAST_LOCATION_UPDATE_KEY]);
  } catch (error) {
    console.error('Error clearing location data:', error);
    throw error;
  }
};

/**
 * Validate location settings
 */
export const validateLocationSettings = (settings: Partial<LocationSettings>): LocationSettings => {
  return {
    userLocation: settings.userLocation || '',
    coordinates: settings.coordinates,
    searchRadius: Math.max(5, Math.min(100, settings.searchRadius || 25)),
    includeRemote: settings.includeRemote !== false,
    autoUpdateLocation: settings.autoUpdateLocation === true
  };
};

/**
 * Cache for geocoded locations to avoid repeated API calls
 */
const geocodeCache = new Map<string, { latitude: number; longitude: number; formattedAddress: string }>();

/**
 * Geocode job location with caching
 */
export const geocodeJobLocation = async (
  jobLocation: string
): Promise<{ latitude: number; longitude: number } | null> => {
  if (!jobLocation || jobLocation.trim() === '') {
    return null;
  }

  // Check if it's a remote job
  const locationLower = jobLocation.toLowerCase();
  if (locationLower.includes('remote') ||
      locationLower.includes('anywhere') ||
      locationLower.includes('work from home')) {
    return null; // Remote jobs don't have coordinates
  }

  // Check cache first
  const cacheKey = jobLocation.toLowerCase().trim();
  if (geocodeCache.has(cacheKey)) {
    const cached = geocodeCache.get(cacheKey)!;
    return { latitude: cached.latitude, longitude: cached.longitude };
  }

  // Geocode the location
  const result = await geocodeLocation(jobLocation);
  if (result) {
    // Cache the result
    geocodeCache.set(cacheKey, result);
    return { latitude: result.latitude, longitude: result.longitude };
  }

  return null;
};

/**
 * Filter jobs by location and radius
 */
export const filterJobsByLocationAndRadius = async (
  jobs: any[],
  userLocation: string,
  userCoordinates: { latitude: number; longitude: number } | null,
  maxDistance: number,
  includeRemote: boolean = true
): Promise<any[]> => {
  if (!userLocation || userLocation.trim() === '') {
    return jobs;
  }

  // If we don't have user coordinates, try to geocode the user location
  let userCoords = userCoordinates;
  if (!userCoords) {
    const geocoded = await geocodeLocation(userLocation);
    if (geocoded) {
      userCoords = { latitude: geocoded.latitude, longitude: geocoded.longitude };
    }
  }

  if (!userCoords) {
    // Fallback to keyword-based filtering if geocoding fails
    return jobs.filter(job => {
      const jobLocation = job.location.toLowerCase();
      const userLocationLower = userLocation.toLowerCase();

      // Include remote jobs if enabled
      if (includeRemote && (jobLocation.includes('remote') || jobLocation.includes('anywhere'))) {
        return true;
      }

      // Simple keyword matching
      return jobLocation.includes(userLocationLower) ||
             userLocationLower.split(/[,\s]+/).some(keyword =>
               keyword.length > 2 && jobLocation.includes(keyword)
             );
    });
  }

  // Filter jobs with distance calculation
  const filteredJobs = [];

  for (const job of jobs) {
    const jobLocation = job.location.toLowerCase();

    // Always include remote jobs if enabled
    if (includeRemote && (jobLocation.includes('remote') || jobLocation.includes('anywhere'))) {
      filteredJobs.push(job);
      continue;
    }

    // Try to get job coordinates
    const jobCoords = await geocodeJobLocation(job.location);
    if (jobCoords) {
      const distance = calculateDistance(
        userCoords.latitude,
        userCoords.longitude,
        jobCoords.latitude,
        jobCoords.longitude
      );

      if (distance <= maxDistance) {
        // Add distance to job for display
        filteredJobs.push({
          ...job,
          distance: `${Math.round(distance)} mi`
        });
      }
    } else {
      // Fallback to keyword matching for jobs we can't geocode
      const userLocationLower = userLocation.toLowerCase();
      if (jobLocation.includes(userLocationLower) ||
          userLocationLower.split(/[,\s]+/).some(keyword =>
            keyword.length > 2 && jobLocation.includes(keyword)
          )) {
        filteredJobs.push(job);
      }
    }
  }

  return filteredJobs;
};

/**
 * Get popular locations with coordinates for quick selection
 */
export const getPopularLocationsWithCoords = async () => {
  const popularCities = [
    'San Francisco, CA',
    'New York, NY',
    'Los Angeles, CA',
    'Seattle, WA',
    'Austin, TX',
    'Boston, MA',
    'Chicago, IL',
    'Denver, CO',
    'Atlanta, GA',
    'Miami, FL',
    'Dallas, TX',
    'Portland, OR',
    'Washington, DC',
    'Philadelphia, PA',
    'Phoenix, AZ'
  ];

  const locationsWithCoords = [];

  for (const city of popularCities) {
    const coords = await geocodeLocation(city);
    if (coords) {
      locationsWithCoords.push({
        name: city,
        coordinates: { latitude: coords.latitude, longitude: coords.longitude },
        formattedAddress: coords.formattedAddress
      });
    }
  }

  return locationsWithCoords;
};

export type { LocationSettings };