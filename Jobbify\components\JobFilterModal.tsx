import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Modal,
  SafeAreaView,
  Alert,
  Dimensions
} from 'react-native';
import { BlurView } from 'expo-blur';
import { FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import {
  JobFilters,
  defaultFilters,
  saveFilterSettings,
  loadFilterSettings,
  getFilterSummary,
  countActiveFilters,
  preferencesToFilters,
  EMPLOYMENT_TYPES,
  JOB_CATEGORIES,
  RADIUS_OPTIONS
} from '@/services/jobFilterService';
import { geocodeLocation } from '@/services/LocationService';
import { getUserJobPreferences } from '@/services/jobRecommendationService';

interface JobFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: JobFilters) => void;
  currentFilters: JobFilters;
}

export default function JobFilterModal({
  visible,
  onClose,
  onApplyFilters,
  currentFilters
}: JobFilterModalProps) {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [filters, setFilters] = useState<JobFilters>(currentFilters);
  const [activeTab, setActiveTab] = useState<'quick' | 'location' | 'employment' | 'category' | 'salary'>('quick');
  const [isValidatingLocation, setIsValidatingLocation] = useState(false);
  const [locationValidation, setLocationValidation] = useState<{
    isValid: boolean;
    message: string;
    coordinates?: { latitude: number; longitude: number };
  } | null>(null);

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters]);

  const handleApplyFilters = async () => {
    if (user?.id) {
      await saveFilterSettings(user.id, filters);
    }
    onApplyFilters(filters);
    onClose();
  };

  const handleResetFilters = () => {
    setFilters(defaultFilters);
  };

  const handleLoadFromPreferences = async () => {
    if (!user?.id) return;
    
    try {
      const preferences = await getUserJobPreferences(user.id);
      if (preferences) {
        const preferencesFilters = preferencesToFilters(preferences);
        setFilters(preferencesFilters);
        Alert.alert('Success', 'Filters loaded from your job preferences!');
      } else {
        Alert.alert('No Preferences', 'You haven\'t set up job preferences yet. Go to Settings to set them up.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load preferences');
    }
  };

  const toggleQuickFilter = (filterKey: keyof JobFilters['quickFilters']) => {
    setFilters(prev => ({
      ...prev,
      quickFilters: {
        ...prev.quickFilters,
        [filterKey]: !prev.quickFilters[filterKey]
      }
    }));
  };

  const toggleEmploymentType = (employmentType: string) => {
    setFilters(prev => ({
      ...prev,
      employmentTypes: prev.employmentTypes.includes(employmentType)
        ? prev.employmentTypes.filter(type => type !== employmentType)
        : [...prev.employmentTypes, employmentType]
    }));
  };

  const toggleJobCategory = (category: string) => {
    setFilters(prev => ({
      ...prev,
      jobCategories: prev.jobCategories.includes(category)
        ? prev.jobCategories.filter(cat => cat !== category)
        : [...prev.jobCategories, category]
    }));
  };

  const validateLocation = async (locationText: string) => {
    if (!locationText || locationText.trim() === '') {
      setLocationValidation(null);
      return;
    }

    // Skip validation for "remote" locations
    if (locationText.toLowerCase().includes('remote')) {
      setLocationValidation({
        isValid: true,
        message: 'Remote work location',
      });
      return;
    }

    setIsValidatingLocation(true);
    try {
      const result = await geocodeLocation(locationText);
      if (result) {
        setLocationValidation({
          isValid: true,
          message: `Found: ${result.formattedAddress}`,
          coordinates: { latitude: result.latitude, longitude: result.longitude }
        });
      } else {
        setLocationValidation({
          isValid: false,
          message: 'Location not found. Try a different format (e.g., "San Francisco, CA")',
        });
      }
    } catch (error) {
      setLocationValidation({
        isValid: false,
        message: 'Error validating location. Please try again.',
      });
    } finally {
      setIsValidatingLocation(false);
    }
  };

  const handleLocationChange = (text: string) => {
    setFilters(prev => ({ ...prev, location: text }));

    // Debounce validation
    const timeoutId = setTimeout(() => {
      validateLocation(text);
    }, 1000);

    return () => clearTimeout(timeoutId);
  };

  const toggleIndustry = (industry: string) => {
    setFilters(prev => ({
      ...prev,
      industries: prev.industries.includes(industry)
        ? prev.industries.filter(ind => ind !== industry)
        : [...prev.industries, industry]
    }));
  };

  const renderTabButton = (tab: typeof activeTab, label: string, icon: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: activeTab === tab ? themeColors.tint : themeColors.card,
          borderColor: activeTab === tab ? themeColors.tint : themeColors.border,
          shadowColor: activeTab === tab ? themeColors.tint : 'transparent',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: activeTab === tab ? 0.3 : 0,
          shadowRadius: 4,
          elevation: activeTab === tab ? 4 : 0,
        }
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <View style={[
        styles.tabIconContainer,
        { backgroundColor: activeTab === tab ? 'rgba(255,255,255,0.2)' : 'transparent' }
      ]}>
        <FontAwesome 
          name={icon as any} 
          size={16} 
          color={activeTab === tab ? '#FFFFFF' : themeColors.textSecondary} 
        />
      </View>
      <Text style={[
        styles.tabButtonText,
        { 
          color: activeTab === tab ? '#FFFFFF' : themeColors.text,
          fontWeight: activeTab === tab ? '600' : '500'
        }
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderQuickFilters = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
          Quick Filters
        </Text>
        <Text style={[styles.sectionDescription, { color: themeColors.textSecondary }]}>
          Apply common filters instantly
        </Text>
      </View>
      
      <View style={styles.quickFiltersGrid}>
        {[
          { key: 'remote', label: 'Remote Work', icon: 'home', description: 'Work from anywhere globally' },
          { key: 'recentlyPosted', label: 'Fresh Jobs', icon: 'clock-o', description: 'Posted within last 7 days' },
          { key: 'hasLogo', label: 'Verified Companies', icon: 'shield', description: 'Companies with verified profiles' }
        ].map(({ key, label, icon, description }) => {
          const isActive = filters.quickFilters[key as keyof typeof filters.quickFilters];
          return (
            <TouchableOpacity
              key={key}
              style={[
                styles.quickFilterCard,
                {
                  backgroundColor: isActive ? themeColors.tint : themeColors.background,
                  borderColor: isActive ? themeColors.tint : themeColors.border,
                  shadowColor: isActive ? themeColors.tint : 'transparent',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: isActive ? 0.2 : 0,
                  shadowRadius: 4,
                  elevation: isActive ? 3 : 0,
                }
              ]}
              onPress={() => toggleQuickFilter(key as keyof JobFilters['quickFilters'])}
            >
              <View style={[
                styles.quickFilterIconContainer,
                { backgroundColor: isActive ? 'rgba(255,255,255,0.2)' : themeColors.tint + '15' }
              ]}>
                <FontAwesome
                  name={icon as any}
                  size={18}
                  color={isActive ? '#FFFFFF' : themeColors.tint}
                />
              </View>
              <View style={styles.quickFilterContent}>
                <Text style={[
                  styles.quickFilterTitle,
                  { color: isActive ? '#FFFFFF' : themeColors.text }
                ]}>
                  {label}
                </Text>
                <Text style={[
                  styles.quickFilterDescription,
                  { color: isActive ? 'rgba(255,255,255,0.8)' : themeColors.textSecondary }
                ]}>
                  {description}
                </Text>
              </View>
              {isActive && (
                <View style={styles.activeIndicator}>
                  <FontAwesome name="check" size={12} color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );

  const renderLocationFilters = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
          Location & Work Style
        </Text>
        <Text style={[styles.sectionDescription, { color: themeColors.textSecondary }]}>
          Set your location preferences and work style
        </Text>
      </View>
      
      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          <FontAwesome name="map-marker" size={14} color={themeColors.tint} /> Location
        </Text>
        <View style={styles.locationInputContainer}>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: locationValidation?.isValid === false ? '#FF6B6B' :
                          locationValidation?.isValid === true ? '#4ECDC4' :
                          themeColors.border
            }]}
            placeholder="Enter city, state, or 'Remote'"
            placeholderTextColor={themeColors.textSecondary}
            value={filters.location}
            onChangeText={handleLocationChange}
          />
          {isValidatingLocation && (
            <View style={styles.validationIndicator}>
              <FontAwesome name="spinner" size={14} color={themeColors.tint} />
              <Text style={[styles.validationText, { color: themeColors.textSecondary }]}>
                Validating location...
              </Text>
            </View>
          )}
          {locationValidation && !isValidatingLocation && (
            <View style={styles.validationIndicator}>
              <FontAwesome
                name={locationValidation.isValid ? "check-circle" : "exclamation-circle"}
                size={14}
                color={locationValidation.isValid ? '#4ECDC4' : '#FF6B6B'}
              />
              <Text style={[
                styles.validationText,
                { color: locationValidation.isValid ? '#4ECDC4' : '#FF6B6B' }
              ]}>
                {locationValidation.message}
              </Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.workStyleContainer}>
        <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
          Work Style Preferences
        </Text>
        
        <View style={styles.workStyleGrid}>
          <TouchableOpacity
            style={[styles.workStyleCard, {
              backgroundColor: filters.remoteOnly ? themeColors.tint : themeColors.card,
              borderColor: filters.remoteOnly ? themeColors.tint : themeColors.border,
            }]}
            onPress={() => setFilters(prev => ({ 
              ...prev, 
              remoteOnly: !prev.remoteOnly,
              inPersonOnly: false // Disable in-person when remote is selected
            }))}
          >
            <FontAwesome 
              name="home" 
              size={20} 
              color={filters.remoteOnly ? '#000000' : themeColors.tint} 
            />
            <Text style={[styles.workStyleTitle, {
              color: filters.remoteOnly ? '#000000' : themeColors.text
            }]}>
              Remote Only
            </Text>
            <Text style={[styles.workStyleDescription, {
              color: filters.remoteOnly ? 'rgba(0,0,0,0.7)' : themeColors.textSecondary
            }]}>
              Work from anywhere
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.workStyleCard, {
              backgroundColor: filters.inPersonOnly ? themeColors.tint : themeColors.card,
              borderColor: filters.inPersonOnly ? themeColors.tint : themeColors.border,
            }]}
            onPress={() => setFilters(prev => ({ 
              ...prev, 
              inPersonOnly: !prev.inPersonOnly,
              remoteOnly: false // Disable remote when in-person is selected
            }))}
          >
            <FontAwesome 
              name="building" 
              size={20} 
              color={filters.inPersonOnly ? '#000000' : themeColors.tint} 
            />
            <Text style={[styles.workStyleTitle, {
              color: filters.inPersonOnly ? '#000000' : themeColors.text
            }]}>
              In-Person Only
            </Text>
            <Text style={[styles.workStyleDescription, {
              color: filters.inPersonOnly ? 'rgba(0,0,0,0.7)' : themeColors.textSecondary
            }]}>
              Office-based roles
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.workStyleCard, {
              backgroundColor: (!filters.remoteOnly && !filters.inPersonOnly) ? themeColors.tint : themeColors.card,
              borderColor: (!filters.remoteOnly && !filters.inPersonOnly) ? themeColors.tint : themeColors.border,
            }]}
            onPress={() => setFilters(prev => ({ 
              ...prev, 
              remoteOnly: false,
              inPersonOnly: false
            }))}
          >
            <FontAwesome 
              name="globe" 
              size={20} 
              color={(!filters.remoteOnly && !filters.inPersonOnly) ? '#000000' : themeColors.tint} 
            />
            <Text style={[styles.workStyleTitle, {
              color: (!filters.remoteOnly && !filters.inPersonOnly) ? '#000000' : themeColors.text
            }]}>
              Hybrid/All
            </Text>
            <Text style={[styles.workStyleDescription, {
              color: (!filters.remoteOnly && !filters.inPersonOnly) ? 'rgba(0,0,0,0.7)' : themeColors.textSecondary
            }]}>
              Any work style
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Search Radius: {filters.maxDistance} miles
        </Text>
        <View style={styles.chipContainer}>
          {RADIUS_OPTIONS.map(radius => (
            <TouchableOpacity
              key={radius}
              style={[
                styles.filterChip,
                {
                  backgroundColor: filters.maxDistance === radius
                    ? themeColors.tint
                    : themeColors.card,
                  borderColor: filters.maxDistance === radius
                    ? themeColors.tint
                    : themeColors.border,
                  borderWidth: 2
                }
              ]}
              onPress={() => setFilters(prev => ({ ...prev, maxDistance: radius }))}
            >
              <Text style={[
                styles.filterChipText,
                {
                  color: filters.maxDistance === radius
                    ? '#FFFFFF'
                    : themeColors.text,
                  fontWeight: filters.maxDistance === radius ? '600' : '500'
                }
              ]}>
                {radius} mi
              </Text>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={[
              styles.filterChip,
              {
                backgroundColor: filters.maxDistance > 100
                  ? themeColors.tint
                  : themeColors.card,
                borderColor: filters.maxDistance > 100
                  ? themeColors.tint
                  : themeColors.border,
                borderWidth: 2
              }
            ]}
            onPress={() => setFilters(prev => ({ ...prev, maxDistance: 999 }))}
          >
            <Text style={[
              styles.filterChipText,
              {
                color: filters.maxDistance > 100
                  ? '#FFFFFF'
                  : themeColors.text,
                fontWeight: filters.maxDistance > 100 ? '600' : '500'
              }
            ]}>
              100+ mi
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderEmploymentTypeFilters = () => (
    <ScrollView style={styles.section} showsVerticalScrollIndicator={false}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
          Employment & Experience
        </Text>
        <Text style={[styles.sectionDescription, { color: themeColors.textSecondary }]}>
          Choose your preferred job types and experience level
        </Text>
      </View>

      <View style={styles.employmentTypeContainer}>
        <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
          <FontAwesome name="briefcase" size={16} color={themeColors.tint} /> Employment Types
        </Text>
        <View style={styles.employmentGrid}>
          {[
            { type: 'Full-time Job', icon: 'clock-o', color: '#4CAF50' },
            { type: 'Part-time Job', icon: 'clock-o', color: '#FF9800' },
            { type: 'Contract', icon: 'file-text-o', color: '#2196F3' },
            { type: 'Internship', icon: 'graduation-cap', color: '#9C27B0' },
            { type: 'Freelance', icon: 'user', color: '#FF5722' },
            { type: 'Remote', icon: 'home', color: '#00BCD4' }
          ].map(({ type, icon, color }) => {
            const isSelected = filters.employmentTypes.includes(type);
            return (
              <TouchableOpacity
                key={type}
                style={[styles.employmentCard, {
                  backgroundColor: isSelected ? themeColors.tint : themeColors.card,
                  borderColor: isSelected ? themeColors.tint : themeColors.border,
                }]}
                onPress={() => toggleEmploymentType(type)}
              >
                <View style={[styles.employmentIconContainer, {
                  backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : color + '15'
                }]}>
                  <FontAwesome
                    name={icon as any}
                    size={18}
                    color={isSelected ? '#000000' : color}
                  />
                </View>
                <Text style={[styles.employmentTitle, {
                  color: isSelected ? '#000000' : themeColors.text
                }]}>
                  {type}
                </Text>
                {isSelected && (
                  <View style={styles.selectedIndicator}>
                    <FontAwesome name="check" size={10} color="#000000" />
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      <View style={styles.experienceLevelContainer}>
        <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
          <FontAwesome name="star" size={16} color={themeColors.tint} /> Experience Level
        </Text>
        <View style={styles.experienceGrid}>
          {[
            { level: 'Entry Level', icon: 'play', description: '0-2 years', color: '#4CAF50' },
            { level: 'Mid Level', icon: 'arrow-up', description: '2-5 years', color: '#FF9800' },
            { level: 'Senior Level', icon: 'trophy', description: '5+ years', color: '#2196F3' },
            { level: 'Executive', icon: 'crown', description: 'Leadership', color: '#9C27B0' }
          ].map(({ level, icon, description, color }) => {
            const isSelected = filters.experienceLevel.includes(level);
            return (
              <TouchableOpacity
                key={level}
                style={[styles.experienceCard, {
                  backgroundColor: isSelected ? themeColors.tint : themeColors.card,
                  borderColor: isSelected ? themeColors.tint : themeColors.border,
                }]}
                onPress={() => {
                  const newFilters = {
                    ...filters,
                    experienceLevel: filters.experienceLevel.includes(level)
                      ? filters.experienceLevel.filter(l => l !== level)
                      : [...filters.experienceLevel, level]
                  };
                  setFilters(newFilters);
                }}
              >
                <View style={[styles.experienceIconContainer, {
                  backgroundColor: isSelected ? 'rgba(255,255,255,0.2)' : color + '15'
                }]}>
                  <FontAwesome
                    name={icon as any}
                    size={16}
                    color={isSelected ? '#000000' : color}
                  />
                </View>
                <Text style={[styles.experienceTitle, {
                  color: isSelected ? '#000000' : themeColors.text
                }]}>
                  {level}
                </Text>
                <Text style={[styles.experienceDescription, {
                  color: isSelected ? 'rgba(0,0,0,0.7)' : themeColors.textSecondary
                }]}>
                  {description}
                </Text>
                {isSelected && (
                  <View style={styles.selectedIndicator}>
                    <FontAwesome name="check" size={10} color="#000000" />
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
       </View>
    </ScrollView>
  );

  const renderJobCategoryFilters = () => (
    <ScrollView style={styles.section} showsVerticalScrollIndicator={false}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Job Categories
      </Text>

      <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
        Select Job Roles/Categories
      </Text>
      <View style={styles.chipContainer}>
        {JOB_CATEGORIES.map(category => (
          <TouchableOpacity
            key={category}
            style={[
              styles.filterChip,
              {
                backgroundColor: filters.jobCategories.includes(category)
                  ? themeColors.tint
                  : themeColors.card,
                borderColor: filters.jobCategories.includes(category)
                  ? themeColors.tint
                  : themeColors.border,
                borderWidth: 2
              }
            ]}
            onPress={() => toggleJobCategory(category)}
          >
            <Text style={[
              styles.filterChipText,
              {
                color: filters.jobCategories.includes(category)
                  ? '#FFFFFF'
                  : themeColors.text,
                fontWeight: filters.jobCategories.includes(category) ? '600' : '500'
              }
            ]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  const renderSalaryFilters = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Salary Range
      </Text>
      
      <View style={styles.salaryContainer}>
        <View style={styles.salaryInputContainer}>
          <Text style={[styles.inputLabel, { color: themeColors.text }]}>
            Minimum Salary
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: themeColors.border
            }]}
            placeholder="e.g., 80000"
            placeholderTextColor={themeColors.textSecondary}
            value={filters.minSalary > 0 ? filters.minSalary.toString() : ''}
            onChangeText={(text) => setFilters(prev => ({
              ...prev,
              minSalary: parseInt(text) || 0
            }))}
            keyboardType="numeric"
          />
        </View>

        <View style={styles.salaryInputContainer}>
          <Text style={[styles.inputLabel, { color: themeColors.text }]}>
            Maximum Salary
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: themeColors.border
            }]}
            placeholder="e.g., 120000"
            placeholderTextColor={themeColors.textSecondary}
            value={filters.maxSalary < 500000 ? filters.maxSalary.toString() : ''}
            onChangeText={(text) => setFilters(prev => ({
              ...prev,
              maxSalary: parseInt(text) || 500000
            }))}
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'quick':
        return renderQuickFilters();
      case 'location':
        return renderLocationFilters();
      case 'employment':
        return renderEmploymentTypeFilters();
      case 'category':
        return renderJobCategoryFilters();
      case 'salary':
        return renderSalaryFilters();
      default:
        return renderQuickFilters();
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        {/* Modern Header with Blur Effect */}
        <BlurView intensity={20} style={[styles.headerBlur, { borderBottomColor: themeColors.border }]}>
          <View style={styles.headerContent}>
            <TouchableOpacity 
              style={[styles.headerButton, { backgroundColor: themeColors.card }]} 
              onPress={onClose}
            >
              <FontAwesome name="times" size={18} color={themeColors.text} />
            </TouchableOpacity>
            
            <View style={styles.headerTitleContainer}>
              <Text style={[styles.headerTitle, { color: themeColors.text }]}>
                Job Filters
              </Text>
              <Text style={[styles.headerSubtitle, { color: themeColors.textSecondary }]}>
                {countActiveFilters(filters)} active filters
              </Text>
            </View>
            
            <TouchableOpacity 
              style={[styles.headerButton, { backgroundColor: themeColors.tint + '20' }]} 
              onPress={handleLoadFromPreferences}
            >
              <FontAwesome name="user" size={18} color={themeColors.tint} />
            </TouchableOpacity>
          </View>
        </BlurView>

        {/* Modern Tab Navigation */}
        <View style={[styles.tabContainer, { backgroundColor: themeColors.background }]}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false} 
            contentContainerStyle={styles.tabScrollContent}
          >
            {renderTabButton('quick', 'Quick', 'flash')}
            {renderTabButton('location', 'Location', 'map-marker')}
            {renderTabButton('employment', 'Employment', 'briefcase')}
            {renderTabButton('category', 'Category', 'tags')}
            {renderTabButton('salary', 'Salary', 'dollar')}
          </ScrollView>
        </View>

        {/* Content with Modern Card Design */}
        <ScrollView 
          style={styles.content} 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.contentContainer}
        >
          <View style={[styles.contentCard, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
            {renderTabContent()}
          </View>
        </ScrollView>

        {/* Modern Footer */}
        <BlurView intensity={20} style={[styles.footerBlur, { borderTopColor: themeColors.border }]}>
          <View style={styles.footerContent}>
            <View style={styles.filterSummary}>
              <Text style={[styles.filterSummaryText, { color: themeColors.text }]}>
                {countActiveFilters(filters)} active filters
              </Text>
              <Text style={[styles.filterSummarySubtext, { color: themeColors.textSecondary }]}>
                {getFilterSummary(filters)}
              </Text>
            </View>
            
            <View style={styles.footerButtons}>
              <TouchableOpacity
                style={[styles.secondaryButton, { 
                  backgroundColor: themeColors.card,
                  borderColor: themeColors.border 
                }]}
                onPress={handleResetFilters}
              >
                <FontAwesome name="refresh" size={16} color={themeColors.text} />
                <Text style={[styles.secondaryButtonText, { color: themeColors.text }]}>
                  Reset
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.primaryButton, { 
                  backgroundColor: themeColors.tint,
                  shadowColor: themeColors.tint,
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 6,
                }]}
                onPress={handleApplyFilters}
              >
                <FontAwesome name="check" size={16} color="#FFFFFF" />
                <Text style={styles.primaryButtonText}>
                  Apply Filters
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Modern Header Styles
  headerBlur: {
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Modern Tab Styles
  tabContainer: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  tabScrollContent: {
    paddingHorizontal: 20,
    gap: 12,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 25,
    borderWidth: 2,
    gap: 8,
    minWidth: 100,
  },
  tabIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabButtonText: {
    fontSize: 13,
    fontWeight: '500',
  },
  // Content Styles
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  contentCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  // Section Styles
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  subsectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    marginTop: 24,
  },
  // Modern Quick Filter Styles
  quickFiltersGrid: {
    gap: 16,
  },
  quickFilterCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 2,
    marginBottom: 8,
  },
  quickFilterIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  quickFilterContent: {
    flex: 1,
  },
  quickFilterTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  quickFilterDescription: {
    fontSize: 13,
    fontWeight: '500',
    lineHeight: 18,
  },
  activeIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginTop: 8,
  },
  sliderButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  // Work Style Styles
  workStyleContainer: {
    marginTop: 24,
  },
  workStyleGrid: {
    gap: 12,
  },
  workStyleCard: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    marginBottom: 12,
    minHeight: 120,
    justifyContent: 'center',
  },
  workStyleTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 4,
    textAlign: 'center',
  },
  workStyleDescription: {
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 18,
  },
  sliderTrack: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  sliderFill: {
    height: '100%',
    borderRadius: 3,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Employment Type Styles
  employmentGrid: {
    gap: 12,
  },
  employmentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 2,
    marginBottom: 8,
    position: 'relative',
  },
  employmentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  employmentContent: {
    flex: 1,
  },
  employmentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  employmentDescription: {
    fontSize: 13,
    fontWeight: '500',
  },
  // Experience Level Styles
  experienceLevelContainer: {
    marginTop: 24,
  },
  experienceGrid: {
    gap: 12,
  },
  experienceCard: {
    flexDirection: 'column',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 2,
    marginBottom: 8,
    position: 'relative',
    minHeight: 100,
    justifyContent: 'center',
  },
  experienceIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  experienceTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
    textAlign: 'center',
  },
  experienceDescription: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  salaryContainer: {
    gap: 16,
  },
  salaryInputContainer: {
    gap: 8,
  },
  // Modern Footer Styles
  footerBlur: {
    borderTopWidth: 1,
  },
  footerContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  filterSummary: {
    marginBottom: 16,
    alignItems: 'center',
  },
  filterSummaryText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  filterSummarySubtext: {
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'center',
  },
  footerButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
    height: 52,
    borderRadius: 16,
    borderWidth: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  primaryButton: {
    flex: 2,
    height: 52,
    borderRadius: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },
  // Location validation styles
  locationInputContainer: {
    gap: 8,
  },
  validationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 4,
  },
  validationText: {
    fontSize: 12,
    fontWeight: '500',
  },
  // Employment type container
  employmentTypeContainer: {
    marginTop: 24,
  },
});
