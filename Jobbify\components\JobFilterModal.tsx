import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Modal,
  SafeAreaView,
  Alert
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import {
  JobFilters,
  defaultFilters,
  saveFilterSettings,
  loadFilterSettings,
  getFilterSummary,
  countActiveFilters,
  preferencesToFilters
} from '@/services/jobFilterService';
import { getUserJobPreferences } from '@/services/jobRecommendationService';

interface JobFilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: JobFilters) => void;
  currentFilters: JobFilters;
}

export default function JobFilterModal({
  visible,
  onClose,
  onApplyFilters,
  currentFilters
}: JobFilterModalProps) {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [filters, setFilters] = useState<JobFilters>(currentFilters);
  const [activeTab, setActiveTab] = useState<'quick' | 'location' | 'job' | 'salary'>('quick');

  useEffect(() => {
    setFilters(currentFilters);
  }, [currentFilters]);

  const handleApplyFilters = async () => {
    if (user?.id) {
      await saveFilterSettings(user.id, filters);
    }
    onApplyFilters(filters);
    onClose();
  };

  const handleResetFilters = () => {
    setFilters(defaultFilters);
  };

  const handleLoadFromPreferences = async () => {
    if (!user?.id) return;
    
    try {
      const preferences = await getUserJobPreferences(user.id);
      if (preferences) {
        const preferencesFilters = preferencesToFilters(preferences);
        setFilters(preferencesFilters);
        Alert.alert('Success', 'Filters loaded from your job preferences!');
      } else {
        Alert.alert('No Preferences', 'You haven\'t set up job preferences yet. Go to Settings to set them up.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load preferences');
    }
  };

  const toggleQuickFilter = (filterKey: keyof JobFilters['quickFilters']) => {
    setFilters(prev => ({
      ...prev,
      quickFilters: {
        ...prev.quickFilters,
        [filterKey]: !prev.quickFilters[filterKey]
      }
    }));
  };

  const toggleJobType = (jobType: string) => {
    setFilters(prev => ({
      ...prev,
      jobTypes: prev.jobTypes.includes(jobType)
        ? prev.jobTypes.filter(type => type !== jobType)
        : [...prev.jobTypes, jobType]
    }));
  };

  const toggleIndustry = (industry: string) => {
    setFilters(prev => ({
      ...prev,
      industries: prev.industries.includes(industry)
        ? prev.industries.filter(ind => ind !== industry)
        : [...prev.industries, industry]
    }));
  };

  const renderTabButton = (tab: typeof activeTab, label: string, icon: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        {
          backgroundColor: activeTab === tab ? themeColors.tint : 'transparent',
          borderColor: themeColors.border
        }
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <FontAwesome 
        name={icon as any} 
        size={16} 
        color={activeTab === tab ? '#FFFFFF' : themeColors.textSecondary} 
      />
      <Text style={[
        styles.tabButtonText,
        { color: activeTab === tab ? '#FFFFFF' : themeColors.textSecondary }
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderQuickFilters = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Quick Filters
      </Text>
      
      <View style={styles.quickFiltersGrid}>
        {[
          { key: 'remote', label: 'Remote Only', icon: 'home' },
          { key: 'fullTime', label: 'Full-Time', icon: 'clock-o' },
          { key: 'highPay', label: 'High Pay ($100k+)', icon: 'dollar' },
          { key: 'recentlyPosted', label: 'Recently Posted', icon: 'calendar' }
        ].map(({ key, label, icon }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.quickFilterChip,
              {
                backgroundColor: filters.quickFilters[key as keyof typeof filters.quickFilters]
                  ? themeColors.tint
                  : themeColors.card,
                borderColor: themeColors.border
              }
            ]}
            onPress={() => toggleQuickFilter(key as keyof JobFilters['quickFilters'])}
          >
            <FontAwesome
              name={icon as any}
              size={14}
              color={filters.quickFilters[key as keyof typeof filters.quickFilters]
                ? '#FFFFFF'
                : themeColors.textSecondary
              }
            />
            <Text style={[
              styles.quickFilterText,
              {
                color: filters.quickFilters[key as keyof typeof filters.quickFilters]
                  ? '#FFFFFF'
                  : themeColors.text
              }
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderLocationFilters = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Location Preferences
      </Text>
      
      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Location
        </Text>
        <TextInput
          style={[styles.textInput, {
            backgroundColor: themeColors.card,
            color: themeColors.text,
            borderColor: themeColors.border
          }]}
          placeholder="Enter city or state"
          placeholderTextColor={themeColors.textSecondary}
          value={filters.location}
          onChangeText={(text) => setFilters(prev => ({ ...prev, location: text }))}
        />
      </View>

      <View style={styles.switchContainer}>
        <Text style={[styles.switchLabel, { color: themeColors.text }]}>
          Remote Only
        </Text>
        <Switch
          value={filters.remoteOnly}
          onValueChange={(value) => setFilters(prev => ({ ...prev, remoteOnly: value }))}
          trackColor={{ false: themeColors.border, true: themeColors.tint }}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Max Distance: {filters.maxDistance} miles
        </Text>
        <View style={styles.sliderContainer}>
          <TouchableOpacity
            style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
            onPress={() => setFilters(prev => ({
              ...prev,
              maxDistance: Math.max(5, prev.maxDistance - 5)
            }))}
          >
            <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>-</Text>
          </TouchableOpacity>
          <View style={[styles.sliderTrack, { backgroundColor: themeColors.border }]}>
            <View
              style={[
                styles.sliderFill,
                {
                  backgroundColor: themeColors.tint,
                  width: `${(filters.maxDistance / 100) * 100}%`
                }
              ]}
            />
          </View>
          <TouchableOpacity
            style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
            onPress={() => setFilters(prev => ({
              ...prev,
              maxDistance: Math.min(100, prev.maxDistance + 5)
            }))}
          >
            <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>+</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderJobFilters = () => (
    <ScrollView style={styles.section} showsVerticalScrollIndicator={false}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Job Type & Industry
      </Text>

      <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
        Job Types
      </Text>
      <View style={styles.chipContainer}>
        {['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'].map(type => (
          <TouchableOpacity
            key={type}
            style={[
              styles.filterChip,
              {
                backgroundColor: filters.jobTypes.includes(type)
                  ? themeColors.tint
                  : themeColors.card,
                borderColor: filters.jobTypes.includes(type)
                  ? themeColors.tint
                  : themeColors.border,
                borderWidth: 2
              }
            ]}
            onPress={() => toggleJobType(type)}
          >
            <Text style={[
              styles.filterChipText,
              {
                color: filters.jobTypes.includes(type)
                  ? '#FFFFFF'
                  : themeColors.text,
                fontWeight: filters.jobTypes.includes(type) ? '600' : '500'
              }
            ]}>
              {type}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
        Industries
      </Text>
      <View style={styles.chipContainer}>
        {['Technology', 'Healthcare', 'Finance', 'Education', 'Marketing', 'Design', 'Sales', 'Engineering', 'Product', 'Operations'].map(industry => (
          <TouchableOpacity
            key={industry}
            style={[
              styles.filterChip,
              {
                backgroundColor: filters.industries.includes(industry)
                  ? themeColors.tint
                  : themeColors.card,
                borderColor: filters.industries.includes(industry)
                  ? themeColors.tint
                  : themeColors.border,
                borderWidth: 2
              }
            ]}
            onPress={() => toggleIndustry(industry)}
          >
            <Text style={[
              styles.filterChipText,
              {
                color: filters.industries.includes(industry)
                  ? '#FFFFFF'
                  : themeColors.text,
                fontWeight: filters.industries.includes(industry) ? '600' : '500'
              }
            ]}>
              {industry}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.subsectionTitle, { color: themeColors.text }]}>
        Experience Level
      </Text>
      <View style={styles.chipContainer}>
        {['Entry Level', 'Mid Level', 'Senior Level', 'Executive'].map(level => (
          <TouchableOpacity
            key={level}
            style={[
              styles.filterChip,
              {
                backgroundColor: filters.experienceLevel.includes(level)
                  ? themeColors.tint
                  : themeColors.card,
                borderColor: filters.experienceLevel.includes(level)
                  ? themeColors.tint
                  : themeColors.border,
                borderWidth: 2
              }
            ]}
            onPress={() => {
              const newFilters = {
                ...filters,
                experienceLevel: filters.experienceLevel.includes(level)
                  ? filters.experienceLevel.filter(l => l !== level)
                  : [...filters.experienceLevel, level]
              };
              setFilters(newFilters);
            }}
          >
            <Text style={[
              styles.filterChipText,
              {
                color: filters.experienceLevel.includes(level)
                  ? '#FFFFFF'
                  : themeColors.text,
                fontWeight: filters.experienceLevel.includes(level) ? '600' : '500'
              }
            ]}>
              {level}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  const renderSalaryFilters = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Salary Range
      </Text>
      
      <View style={styles.salaryContainer}>
        <View style={styles.salaryInputContainer}>
          <Text style={[styles.inputLabel, { color: themeColors.text }]}>
            Minimum Salary
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: themeColors.border
            }]}
            placeholder="e.g., 80000"
            placeholderTextColor={themeColors.textSecondary}
            value={filters.minSalary > 0 ? filters.minSalary.toString() : ''}
            onChangeText={(text) => setFilters(prev => ({
              ...prev,
              minSalary: parseInt(text) || 0
            }))}
            keyboardType="numeric"
          />
        </View>

        <View style={styles.salaryInputContainer}>
          <Text style={[styles.inputLabel, { color: themeColors.text }]}>
            Maximum Salary
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: themeColors.border
            }]}
            placeholder="e.g., 120000"
            placeholderTextColor={themeColors.textSecondary}
            value={filters.maxSalary < 500000 ? filters.maxSalary.toString() : ''}
            onChangeText={(text) => setFilters(prev => ({
              ...prev,
              maxSalary: parseInt(text) || 500000
            }))}
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'quick':
        return renderQuickFilters();
      case 'location':
        return renderLocationFilters();
      case 'job':
        return renderJobFilters();
      case 'salary':
        return renderSalaryFilters();
      default:
        return renderQuickFilters();
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: themeColors.border }]}>
          <TouchableOpacity onPress={onClose}>
            <FontAwesome name="times" size={20} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            Job Filters
          </Text>
          <TouchableOpacity onPress={handleLoadFromPreferences}>
            <FontAwesome name="user" size={20} color={themeColors.tint} />
          </TouchableOpacity>
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          {renderTabButton('quick', 'Quick', 'flash')}
          {renderTabButton('location', 'Location', 'map-marker')}
          {renderTabButton('job', 'Job Type', 'briefcase')}
          {renderTabButton('salary', 'Salary', 'dollar')}
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderTabContent()}
        </ScrollView>

        {/* Footer */}
        <View style={[styles.footer, { backgroundColor: themeColors.background, borderTopColor: themeColors.border }]}>
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: themeColors.border }]}
            onPress={handleResetFilters}
          >
            <Text style={[styles.secondaryButtonText, { color: themeColors.text }]}>
              Reset
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: themeColors.tint }]}
            onPress={handleApplyFilters}
          >
            <Text style={styles.primaryButtonText}>
              Apply Filters ({countActiveFilters(filters)})
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 8,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  tabButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    marginTop: 16,
  },
  quickFiltersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
    minWidth: '45%',
  },
  quickFilterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginTop: 8,
  },
  sliderButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  sliderTrack: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  sliderFill: {
    height: '100%',
    borderRadius: 3,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  salaryContainer: {
    gap: 16,
  },
  salaryInputContainer: {
    gap: 8,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  primaryButton: {
    flex: 2,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
