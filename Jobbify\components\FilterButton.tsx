import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { JobFilters, countActiveFilters } from '@/services/jobFilterService';

interface FilterButtonProps {
  filters: JobFilters;
  onPress: () => void;
}

export default function FilterButton({ filters, onPress }: FilterButtonProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const activeFilterCount = countActiveFilters(filters);

  return (
    <TouchableOpacity
      style={[
        styles.filterButton,
        { 
          backgroundColor: themeColors.card,
          borderColor: themeColors.border,
          shadowColor: themeColors.text
        }
      ]}
      onPress={onPress}
    >
      <FontAwesome 
        name="filter" 
        size={18} 
        color={activeFilterCount > 0 ? themeColors.tint : themeColors.textSecondary} 
      />
      {activeFilterCount > 0 && (
        <View style={[styles.badge, { backgroundColor: themeColors.tint }]}>
          <Text style={styles.badgeText}>{activeFilterCount}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  filterButton: {
    position: 'absolute',
    top: 60, // Below status bar
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 11,
    fontWeight: '700',
  },
});