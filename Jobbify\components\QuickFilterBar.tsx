import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { JobFilters, countActiveFilters } from '@/services/jobFilterService';

interface QuickFilterBarProps {
  filters: JobFilters;
  onFilterPress: () => void;
  onQuickFilterToggle: (filterKey: keyof JobFilters['quickFilters']) => void;
  onLocationPress: () => void;
  onLocationSettingsPress?: () => void;
  userLocation?: string;
}

export default function QuickFilterBar({
  filters,
  onFilterPress,
  onQuickFilterToggle,
  onLocationPress,
  onLocationSettingsPress,
  userLocation
}: QuickFilterBarProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const activeFilterCount = countActiveFilters(filters);

  const quickFilters = [
    {
      key: 'remote' as const,
      label: 'Remote',
      icon: 'home',
      active: filters.quickFilters.remote
    },
    {
      key: 'recentlyPosted' as const,
      label: 'Recent',
      icon: 'calendar',
      active: filters.quickFilters.recentlyPosted
    },
    {
      key: 'hasLogo' as const,
      label: 'With Logo',
      icon: 'image',
      active: filters.quickFilters.hasLogo
    }
  ];

  // Add work style indicators
  const workStyleFilters = [];
  if (filters.remoteOnly) {
    workStyleFilters.push({
      key: 'remoteOnly',
      label: 'Remote Only',
      icon: 'home',
      active: true
    });
  }
  if (filters.inPersonOnly) {
    workStyleFilters.push({
      key: 'inPersonOnly',
      label: 'In-Person Only',
      icon: 'building',
      active: true
    });
  }

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      <BlurView
        tint={theme === 'dark' ? 'dark' : 'light'}
        intensity={95}
        style={styles.blurContainer}
      >
        {/* Main Filter Button */}
        <TouchableOpacity
          style={[
            styles.filterButton,
            { 
              backgroundColor: themeColors.card,
              borderColor: themeColors.border
            }
          ]}
          onPress={onFilterPress}
        >
        <FontAwesome 
          name="filter" 
          size={16} 
          color={activeFilterCount > 0 ? themeColors.tint : themeColors.textSecondary} 
        />
        <Text style={[
          styles.filterButtonText,
          { 
            color: activeFilterCount > 0 ? themeColors.tint : themeColors.textSecondary 
          }
        ]}>
          Filters
        </Text>
        {activeFilterCount > 0 && (
          <View style={[styles.badge, { backgroundColor: themeColors.tint }]}>
            <Text style={styles.badgeText}>{activeFilterCount}</Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Location Button */}
      <TouchableOpacity
        style={[
          styles.locationButton,
          { 
            backgroundColor: filters.location || userLocation ? themeColors.tint : themeColors.card,
            borderColor: themeColors.border
          }
        ]}
        onPress={onLocationPress}
      >
        <FontAwesome 
          name="map-marker" 
          size={16} 
          color={filters.location || userLocation ? '#FFFFFF' : themeColors.textSecondary} 
        />
        <Text style={[
          styles.locationButtonText,
          { 
            color: filters.location || userLocation ? '#FFFFFF' : themeColors.textSecondary 
          }
        ]} numberOfLines={1}>
          {filters.location || userLocation || 'Location'}
        </Text>
      </TouchableOpacity>

      {/* Location Settings Button */}
      {onLocationSettingsPress && (
        <TouchableOpacity
          style={[
            styles.settingsButton,
            { 
              backgroundColor: themeColors.card,
              borderColor: themeColors.border
            }
          ]}
          onPress={onLocationSettingsPress}
        >
          <FontAwesome 
            name="cog" 
            size={16} 
            color={themeColors.textSecondary} 
          />
        </TouchableOpacity>
      )}

      {/* Quick Filter Chips */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.quickFiltersContainer}
        contentContainerStyle={styles.quickFiltersContent}
      >
        {/* Work Style Filters (Remote/In-Person) */}
        {workStyleFilters.map(({ key, label, icon, active }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.quickFilterChip,
              {
                backgroundColor: active ? '#FF6B35' : themeColors.card, // Orange for work style
                borderColor: active ? '#FF6B35' : themeColors.border
              }
            ]}
            onPress={() => {}} // These are read-only indicators
          >
            <FontAwesome
              name={icon as any}
              size={12}
              color={active ? '#FFFFFF' : themeColors.textSecondary}
            />
            <Text style={[
              styles.quickFilterText,
              { color: active ? '#FFFFFF' : themeColors.text }
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}

        {/* Regular Quick Filters */}
        {quickFilters.map(({ key, label, icon, active }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.quickFilterChip,
              {
                backgroundColor: active ? themeColors.tint : themeColors.card,
                borderColor: themeColors.border
              }
            ]}
            onPress={() => onQuickFilterToggle(key)}
          >
            <FontAwesome
              name={icon as any}
              size={12}
              color={active ? '#FFFFFF' : themeColors.textSecondary}
            />
            <Text style={[
              styles.quickFilterText,
              { color: active ? '#FFFFFF' : themeColors.text }
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      </BlurView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    zIndex: 1000,
    elevation: 10,
    position: 'relative',
  },
  blurContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: -16,
    marginVertical: -12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
    zIndex: 100,
  },
  filterButtonText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  badge: {
    minWidth: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    gap: 8,
    flex: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
    zIndex: 100,
  },
  locationButtonText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  settingsButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 44,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
    marginLeft: 8,
  },
  quickFiltersContainer: {
    marginTop: 8,
    zIndex: 50,
  },
  quickFiltersContent: {
    gap: 10,
    paddingRight: 16,
  },
  quickFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 2,
    gap: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.12,
    shadowRadius: 4,
    elevation: 6,
    zIndex: 50,
  },
  quickFilterText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
