import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import { JobFilters, countActiveFilters } from '@/services/jobFilterService';

interface QuickFilterBarProps {
  filters: JobFilters;
  onFilterPress: () => void;
  onQuickFilterToggle: (filterKey: keyof JobFilters['quickFilters']) => void;
  onLocationPress: () => void;
  onLocationSettingsPress?: () => void;
  userLocation?: string;
}

export default function QuickFilterBar({
  filters,
  onFilterPress,
  onQuickFilterToggle,
  onLocationPress,
  onLocationSettingsPress,
  userLocation
}: QuickFilterBarProps) {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const activeFilterCount = countActiveFilters(filters);

  const quickFilters = [
    { 
      key: 'remote' as const, 
      label: 'Remote', 
      icon: 'home',
      active: filters.quickFilters.remote 
    },
    { 
      key: 'fullTime' as const, 
      label: 'Full-Time', 
      icon: 'clock-o',
      active: filters.quickFilters.fullTime 
    },
    { 
      key: 'highPay' as const, 
      label: 'High Pay', 
      icon: 'dollar',
      active: filters.quickFilters.highPay 
    },
    { 
      key: 'recentlyPosted' as const, 
      label: 'Recent', 
      icon: 'calendar',
      active: filters.quickFilters.recentlyPosted 
    }
  ];

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      {/* Main Filter Button */}
      <TouchableOpacity
        style={[
          styles.filterButton,
          { 
            backgroundColor: themeColors.card,
            borderColor: themeColors.border
          }
        ]}
        onPress={onFilterPress}
      >
        <FontAwesome 
          name="filter" 
          size={16} 
          color={activeFilterCount > 0 ? themeColors.tint : themeColors.textSecondary} 
        />
        <Text style={[
          styles.filterButtonText,
          { 
            color: activeFilterCount > 0 ? themeColors.tint : themeColors.textSecondary 
          }
        ]}>
          Filters
        </Text>
        {activeFilterCount > 0 && (
          <View style={[styles.badge, { backgroundColor: themeColors.tint }]}>
            <Text style={styles.badgeText}>{activeFilterCount}</Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Location Button */}
      <TouchableOpacity
        style={[
          styles.locationButton,
          { 
            backgroundColor: filters.location || userLocation ? themeColors.tint : themeColors.card,
            borderColor: themeColors.border
          }
        ]}
        onPress={onLocationPress}
      >
        <FontAwesome 
          name="map-marker" 
          size={16} 
          color={filters.location || userLocation ? '#FFFFFF' : themeColors.textSecondary} 
        />
        <Text style={[
          styles.locationButtonText,
          { 
            color: filters.location || userLocation ? '#FFFFFF' : themeColors.textSecondary 
          }
        ]} numberOfLines={1}>
          {filters.location || userLocation || 'Location'}
        </Text>
      </TouchableOpacity>

      {/* Location Settings Button */}
      {onLocationSettingsPress && (
        <TouchableOpacity
          style={[
            styles.settingsButton,
            { 
              backgroundColor: themeColors.card,
              borderColor: themeColors.border
            }
          ]}
          onPress={onLocationSettingsPress}
        >
          <FontAwesome 
            name="cog" 
            size={16} 
            color={themeColors.textSecondary} 
          />
        </TouchableOpacity>
      )}

      {/* Quick Filter Chips */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.quickFiltersContainer}
        contentContainerStyle={styles.quickFiltersContent}
      >
        {quickFilters.map(({ key, label, icon, active }) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.quickFilterChip,
              {
                backgroundColor: active ? themeColors.tint : themeColors.card,
                borderColor: themeColors.border
              }
            ]}
            onPress={() => onQuickFilterToggle(key)}
          >
            <FontAwesome
              name={icon as any}
              size={12}
              color={active ? '#FFFFFF' : themeColors.textSecondary}
            />
            <Text style={[
              styles.quickFilterText,
              { color: active ? '#FFFFFF' : themeColors.text }
            ]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  filterButtonText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  badge: {
    minWidth: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    gap: 8,
    flex: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  locationButtonText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  settingsButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 44,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
    marginLeft: 8,
  },
  quickFiltersContainer: {
    marginTop: 8,
  },
  quickFiltersContent: {
    gap: 10,
    paddingRight: 16,
  },
  quickFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 2,
    gap: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  quickFilterText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
