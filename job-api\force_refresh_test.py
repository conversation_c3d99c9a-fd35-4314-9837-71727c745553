#!/usr/bin/env python3
from job_service import fetch_and_store_jobs

def test_fetch_and_store():
    print("🔧 Testing fetch_and_store_jobs with new APIs...")
    try:
        total = fetch_and_store_jobs()
        print(f"✅ Successfully fetched and stored {total} jobs from all APIs including Jooble and The Muse!")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_fetch_and_store()
