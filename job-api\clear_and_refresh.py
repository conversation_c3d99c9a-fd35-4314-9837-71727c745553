#!/usr/bin/env python3
from job_service import supabase, fetch_and_store_jobs

def clear_and_refresh():
    """Clear all jobs and refresh with new API data"""
    print("🗑️ Clearing existing jobs...")
    
    try:
        # Get all job IDs first
        all_jobs = supabase.table("jobs").select("id").execute()
        job_ids = [job["id"] for job in all_jobs.data]
        
        if job_ids:
            # Delete in batches to avoid issues
            batch_size = 100
            for i in range(0, len(job_ids), batch_size):
                batch = job_ids[i:i + batch_size]
                for job_id in batch:
                    supabase.table("jobs").delete().eq("id", job_id).execute()
            
            print(f"✅ Cleared {len(job_ids)} existing jobs")
        else:
            print("✅ No existing jobs to clear")
        
        print("\n🔄 Fetching fresh jobs from all APIs...")
        total = fetch_and_store_jobs()
        print(f"✅ Added {total} fresh jobs from all sources including Jooble and The Muse!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    clear_and_refresh()
